/**
 * Cloudflare Function for Image Upload to Bunny Storage
 * This works with static Astro sites on Cloudflare Pages
 */

// Bunny Storage configuration
function getBunnyStorageConfig(env) {
  const storageZoneName = env.BUNNY_STORAGE_ZONE_NAME;
  const apiKey = env.BUNNY_STORAGE_API_KEY;
  const region = env.BUNNY_STORAGE_REGION;
  const baseUrl = env.BUNNY_STORAGE_BASE_URL;

  if (!storageZoneName || !apiKey || !region || !baseUrl) {
    throw new Error('Missing required Bunny Storage configuration');
  }

  return { storageZoneName, apiKey, region, baseUrl };
}

// Get storage endpoint based on region
function getStorageEndpoint(region) {
  const endpoints = {
    'de': 'storage.bunnycdn.com',
    'uk': 'uk.storage.bunnycdn.com',
    'ny': 'ny.storage.bunnycdn.com',
    'la': 'la.storage.bunnycdn.com',
    'sg': 'sg.storage.bunnycdn.com',
    'se': 'se.storage.bunnycdn.com',
    'br': 'br.storage.bunnycdn.com',
    'jh': 'jh.storage.bunnycdn.com',
    'syd': 'syd.storage.bunnycdn.com'
  };

  return endpoints[region.toLowerCase()] || 'storage.bunnycdn.com';
}

// Generate unique filename
function generateUniqueFilename(originalName) {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
  const baseName = originalName.split('.')[0].replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
  
  return `${baseName}-${timestamp}-${randomSuffix}.${extension}`;
}

// Validate image file
function validateImageFile(file) {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
    };
  }

  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size too large. Maximum size is 10MB.'
    };
  }

  return { valid: true };
}

// Upload file to Bunny Storage with custom filename
async function uploadToBunnyStorage(file, config, path = 'products', customFilename = null) {
  try {
    const validation = validateImageFile(file);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    const filename = customFilename || generateUniqueFilename(file.name);
    const storageEndpoint = getStorageEndpoint(config.region);
    const uploadUrl = `https://${storageEndpoint}/${config.storageZoneName}/${path}/${filename}`;

    const fileBuffer = await file.arrayBuffer();

    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'AccessKey': config.apiKey,
        'Content-Type': 'application/octet-stream'
      },
      body: fileBuffer
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: `Upload failed: ${response.status} ${response.statusText}. ${errorText}`
      };
    }

    const cdnUrl = `${config.baseUrl}/${path}/${filename}`;
    return {
      success: true,
      url: cdnUrl,
      filename: filename,
      size: file.size
    };

  } catch (error) {
    console.error('Bunny Storage upload error:', error);
    return {
      success: false,
      error: error.message || 'Unknown upload error'
    };
  }
}

// Main function handler
export async function onRequestPost(context) {
  const { request, env } = context;

  try {
    // Debug environment variables
    const envCheck = {
      BUNNY_STORAGE_ZONE_NAME: !!env.BUNNY_STORAGE_ZONE_NAME,
      BUNNY_STORAGE_API_KEY: !!env.BUNNY_STORAGE_API_KEY,
      BUNNY_STORAGE_REGION: !!env.BUNNY_STORAGE_REGION,
      BUNNY_STORAGE_BASE_URL: !!env.BUNNY_STORAGE_BASE_URL
    };

    console.log('Environment variables check:', envCheck);

    // Check content type
    const contentType = request.headers.get('content-type');
    if (!contentType || !contentType.includes('multipart/form-data')) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid content type. Expected multipart/form-data.',
          debug: { envCheck, contentType }
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get Bunny Storage config
    const config = getBunnyStorageConfig(env);

    // Parse form data
    const formData = await request.formData();
    const files = formData.getAll('images');

    if (!files || files.length === 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'No files provided.'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Upload files (now supporting image variants)
    const uploadResults = [];
    const errors = [];

    for (const file of files) {
      if (file.size === 0) continue;

      try {
        // Check if this is a variant file (has variant data in the name)
        const fileName = file.name;
        let customFilename = null;

        // If the file name contains variant info, extract it
        if (fileName.includes('|')) {
          const parts = fileName.split('|');
          customFilename = parts[0]; // Use the custom filename
        }

        const result = await uploadToBunnyStorage(file, config, 'products', customFilename);

        if (result.success) {
          uploadResults.push({
            filename: fileName,
            originalName: file.name,
            url: result.url,
            size: result.size,
            success: true
          });
        } else {
          errors.push({
            filename: fileName,
            error: result.error,
            success: false
          });
        }
      } catch (error) {
        errors.push({
          filename: file.name,
          error: error.message || 'Unknown error',
          success: false
        });
      }
    }

    // Return results
    const response = {
      success: uploadResults.length > 0,
      uploaded: uploadResults,
      errors: errors,
      totalFiles: files.length,
      successCount: uploadResults.length,
      errorCount: errors.length
    };

    const statusCode = uploadResults.length > 0 ? 200 : 400;

    return new Response(
      JSON.stringify(response),
      {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Upload function error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error during upload',
        details: error.message || 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle GET requests for testing
export async function onRequestGet(context) {
  const { env } = context;
  
  const envCheck = {
    BUNNY_STORAGE_ZONE_NAME: !!env.BUNNY_STORAGE_ZONE_NAME,
    BUNNY_STORAGE_API_KEY: !!env.BUNNY_STORAGE_API_KEY,
    BUNNY_STORAGE_REGION: !!env.BUNNY_STORAGE_REGION,
    BUNNY_STORAGE_BASE_URL: !!env.BUNNY_STORAGE_BASE_URL
  };

  return new Response(
    JSON.stringify({
      success: false,
      error: 'This endpoint only accepts POST requests with multipart/form-data.',
      method: 'GET',
      environmentVariables: envCheck,
      timestamp: new Date().toISOString()
    }),
    {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}
