/**
 * Advanced Image Optimization Utilities
 * Reduces image waste by implementing smart sizing and format selection
 */

export interface ImageOptimizationConfig {
  quality: number;
  format: 'webp' | 'avif' | 'auto';
  enablePlaceholder: boolean;
  enableLazyLoading: boolean;
  enableResponsive: boolean;
}

export interface ResponsiveImageSizes {
  mobile: { width: number; height: number };
  tablet: { width: number; height: number };
  desktop: { width: number; height: number };
  large: { width: number; height: number };
}

export interface OptimizedImageResult {
  src: string;
  srcset: string;
  sizes: string;
  placeholder?: string;
  width: number;
  height: number;
}

/**
 * Default configuration optimized for performance
 */
export const DEFAULT_CONFIG: ImageOptimizationConfig = {
  quality: 85,
  format: 'auto',
  enablePlaceholder: true,
  enableLazyLoading: true,
  enableResponsive: true
};

/**
 * Optimized breakpoints based on real-world usage patterns
 * Updated to match actual image dimensions (600x450) and display sizes
 */
export const OPTIMIZED_BREAKPOINTS: ResponsiveImageSizes = {
  mobile: { width: 320, height: 240 },
  tablet: { width: 480, height: 360 },
  desktop: { width: 600, height: 450 },
  large: { width: 600, height: 450 } // Never exceed source image size
};

/**
 * Product-specific breakpoints optimized for grid layout
 * Based on actual grid card sizes: 280-500px wide with 4:3 aspect ratio
 */
export const PRODUCT_BREAKPOINTS: ResponsiveImageSizes = {
  mobile: { width: 300, height: 225 },   // For ~300px wide cards
  tablet: { width: 400, height: 300 },   // For ~400px wide cards
  desktop: { width: 500, height: 375 },  // For ~500px wide cards
  large: { width: 600, height: 450 }     // Maximum source size
};

/**
 * Product detail page breakpoints optimized for high-quality viewing
 * Mobile and tablet are optimized for performance, desktop and large use original images
 * Using 0 dimensions indicates original, unresized image should be served
 */
export const PRODUCT_DETAIL_BREAKPOINTS: ResponsiveImageSizes = {
  mobile: { width: 400, height: 300 },    // Better mobile experience
  tablet: { width: 600, height: 450 },    // Good tablet viewing
  desktop: { width: 0, height: 0 },       // Original image for desktop
  large: { width: 0, height: 0 }          // Original image for large displays
};

/**
 * Zoomed image configuration for full-resolution viewing
 * Uses very large dimensions to ensure original image quality is preserved
 */
export const ZOOMED_IMAGE_CONFIG = {
  width: 3000,   // Large enough to not resize most images
  height: 3000,  // Large enough to not resize most images
  quality: 95,   // Higher quality for zoom viewing
  format: 'auto' as const
};

/**
 * Detect if image is from Bunny CDN
 */
export function isBunnyCDNImage(url: string): boolean {
  return url.includes('b-cdn.net') || url.includes('bunnycdn.com');
}

/**
 * Detect if image is from Unsplash
 */
export function isUnsplashImage(url: string): boolean {
  return url.includes('unsplash.com') || url.includes('images.unsplash.com');
}

/**
 * Generate optimized Bunny CDN URL with cache optimization
 * If width and height are 0, serves original image without resizing
 */
export function generateBunnyCDNUrl(
  baseUrl: string,
  width: number,
  height: number,
  config: Partial<ImageOptimizationConfig> = {}
): string {
  const { quality = 85, format = 'auto' } = { ...DEFAULT_CONFIG, ...config };

  const cleanUrl = baseUrl.split('?')[0];
  const params = new URLSearchParams();

  // Only add width/height if not requesting original (0 means original)
  if (width > 0 && height > 0) {
    params.set('width', width.toString());
    params.set('height', height.toString());
    params.set('fit', 'cover');
    params.set('crop', 'smart');
  }

  // Always add quality and format optimization
  params.set('quality', quality.toString());
  params.set('format', format);

  // Add cache optimization parameters
  params.set('cache-control', 'public,max-age=31536000,immutable');
  params.set('expires', new Date(Date.now() + 31536000000).toISOString()); // 1 year
  params.set('v', '1.0'); // Version for cache busting when needed

  return `${cleanUrl}?${params.toString()}`;
}

/**
 * Generate cache-optimized URL for any CDN
 */
export function generateCacheOptimizedUrl(
  baseUrl: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: string;
    version?: string;
    maxAge?: number;
  } = {}
): string {
  const {
    width,
    height,
    quality = 85,
    format = 'auto',
    version = '1.0',
    maxAge = 31536000 // 1 year default
  } = options;

  const cleanUrl = baseUrl.split('?')[0];
  const params = new URLSearchParams();

  // Add optimization parameters
  if (width) params.set('width', width.toString());
  if (height) params.set('height', height.toString());
  params.set('quality', quality.toString());
  params.set('format', format);
  params.set('fit', 'cover');
  params.set('crop', 'smart');

  // Add cache optimization
  params.set('cache-control', `public,max-age=${maxAge},immutable`);
  params.set('v', version);

  return `${cleanUrl}?${params.toString()}`;
}

/**
 * Generate optimized Unsplash URL
 * If width and height are 0, serves original image without resizing
 */
export function generateUnsplashUrl(
  baseUrl: string,
  width: number,
  height: number,
  config: Partial<ImageOptimizationConfig> = {}
): string {
  const { quality = 85, format = 'auto' } = { ...DEFAULT_CONFIG, ...config };

  const cleanUrl = baseUrl.split('?')[0];
  const params = new URLSearchParams();

  // Only add width/height if not requesting original (0 means original)
  if (width > 0 && height > 0) {
    params.set('w', width.toString());
    params.set('h', height.toString());
    params.set('fit', 'crop');
    params.set('crop', 'smart');
  }

  // Always add quality and format optimization
  params.set('q', quality.toString());
  params.set('auto', 'format');

  if (format === 'webp' || format === 'auto') {
    params.set('fm', 'webp');
  }

  return `${cleanUrl}?${params.toString()}`;
}

/**
 * Generate responsive image URLs for different breakpoints
 */
export function generateResponsiveUrls(
  baseUrl: string,
  breakpoints: ResponsiveImageSizes = OPTIMIZED_BREAKPOINTS,
  config: Partial<ImageOptimizationConfig> = {}
): Record<string, string> {
  const urls: Record<string, string> = {};
  
  Object.entries(breakpoints).forEach(([key, { width, height }]) => {
    if (isBunnyCDNImage(baseUrl)) {
      urls[key] = generateBunnyCDNUrl(baseUrl, width, height, config);
    } else if (isUnsplashImage(baseUrl)) {
      urls[key] = generateUnsplashUrl(baseUrl, width, height, config);
    } else {
      // For other CDNs, use basic optimization
      urls[key] = baseUrl;
    }
  });
  
  return urls;
}

/**
 * Generate srcset string for responsive images
 * Handles original images (width 0) by using a large width descriptor
 */
export function generateSrcSet(
  baseUrl: string,
  breakpoints: ResponsiveImageSizes = OPTIMIZED_BREAKPOINTS,
  config: Partial<ImageOptimizationConfig> = {}
): string {
  const urls = generateResponsiveUrls(baseUrl, breakpoints, config);

  return Object.entries(breakpoints)
    .map(([key, { width }]) => {
      // For original images (width 0), use a large width descriptor
      const widthDescriptor = width > 0 ? width : 2000;
      return `${urls[key]} ${widthDescriptor}w`;
    })
    .join(', ');
}

/**
 * Generate sizes attribute for responsive images
 */
export function generateSizesAttribute(context: 'product-grid' | 'product-detail' | 'hero'): string {
  switch (context) {
    case 'product-grid':
      // Updated to match actual grid card sizes: 280-500px
      return '(max-width: 480px) 300px, (max-width: 768px) 400px, (max-width: 1200px) 500px, 500px';
    case 'product-detail':
      // Mobile and tablet optimized, desktop gets original image
      return '(max-width: 480px) 400px, (max-width: 768px) 600px, 100vw';
    case 'hero':
      return '(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px';
    default:
      return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';
  }
}

/**
 * Generate low-quality placeholder image
 */
export function generatePlaceholder(
  baseUrl: string,
  width: number = 50,
  height: number = 38
): string {
  if (isBunnyCDNImage(baseUrl)) {
    return generateBunnyCDNUrl(baseUrl, width, height, { quality: 20, format: 'webp' });
  } else if (isUnsplashImage(baseUrl)) {
    return generateUnsplashUrl(baseUrl, width, height, { quality: 20, format: 'webp' });
  }
  
  return baseUrl;
}

/**
 * Generate zoomed image URL for full-resolution viewing
 * Uses high quality settings and large dimensions to preserve original image quality
 */
export function generateZoomedImageUrl(
  baseUrl: string,
  config: Partial<ImageOptimizationConfig> = {}
): string {
  const finalConfig = {
    ...DEFAULT_CONFIG,
    ...config,
    quality: ZOOMED_IMAGE_CONFIG.quality,
    format: ZOOMED_IMAGE_CONFIG.format
  };

  if (isBunnyCDNImage(baseUrl)) {
    return generateBunnyCDNUrl(
      baseUrl,
      ZOOMED_IMAGE_CONFIG.width,
      ZOOMED_IMAGE_CONFIG.height,
      finalConfig
    );
  } else if (isUnsplashImage(baseUrl)) {
    return generateUnsplashUrl(
      baseUrl,
      ZOOMED_IMAGE_CONFIG.width,
      ZOOMED_IMAGE_CONFIG.height,
      finalConfig
    );
  } else {
    // For other CDNs, return the original URL with cache optimization
    return generateCacheOptimizedUrl(baseUrl, {
      quality: finalConfig.quality,
      format: finalConfig.format,
      version: '1.0'
    });
  }
}

/**
 * Main function to optimize any image URL
 */
export function optimizeImage(
  baseUrl: string,
  context: 'product-grid' | 'product-detail' | 'hero' = 'product-grid',
  config: Partial<ImageOptimizationConfig> = {}
): OptimizedImageResult {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  // Select appropriate breakpoints based on context
  let breakpoints: ResponsiveImageSizes;
  if (context === 'product-grid') {
    breakpoints = PRODUCT_BREAKPOINTS;
  } else if (context === 'product-detail') {
    breakpoints = PRODUCT_DETAIL_BREAKPOINTS;
  } else {
    breakpoints = OPTIMIZED_BREAKPOINTS;
  }

  // Generate responsive URLs
  const responsiveUrls = generateResponsiveUrls(baseUrl, breakpoints, finalConfig);
  const srcset = generateSrcSet(baseUrl, breakpoints, finalConfig);
  const sizes = generateSizesAttribute(context);

  // Use desktop size as default
  const defaultSize = breakpoints.desktop;
  const src = responsiveUrls.desktop || baseUrl;

  // Generate placeholder if enabled
  const placeholder = finalConfig.enablePlaceholder
    ? generatePlaceholder(baseUrl, 50, Math.round(50 * (defaultSize.height / defaultSize.width)))
    : undefined;

  return {
    src,
    srcset,
    sizes,
    placeholder,
    width: defaultSize.width,
    height: defaultSize.height
  };
}

/**
 * Utility to calculate optimal image dimensions based on container size
 */
export function calculateOptimalDimensions(
  containerWidth: number,
  containerHeight: number,
  devicePixelRatio: number = 1
): { width: number; height: number } {
  // Account for device pixel ratio but cap at 2x for performance
  const effectiveRatio = Math.min(devicePixelRatio, 2);
  
  return {
    width: Math.round(containerWidth * effectiveRatio),
    height: Math.round(containerHeight * effectiveRatio)
  };
}

/**
 * Check if WebP is supported (for client-side usage)
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Check if AVIF is supported (for client-side usage)
 */
export function supportsAVIF(): Promise<boolean> {
  return new Promise((resolve) => {
    const avif = new Image();
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2);
    };
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
}
